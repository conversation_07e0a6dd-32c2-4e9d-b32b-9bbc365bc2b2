import { DynamicBreadcrumb } from "@/layout/components/breadcrumb/dynamic-breadcrumb";
import { LoadingContainer } from "@/layout/components/container/loading-container";
import { MainContainer } from "@/layout/components/container/main-container";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { Suspense } from "react";

export default function Layout({ children }: IReactChildrenType) {
	return (
		<Suspense fallback={<LoadingContainer />}>
			<MainContainer>
				<div className="px-3 py-2">
					<DynamicBreadcrumb />
				</div>
				{children}
			</MainContainer>
		</Suspense>
	);
}
