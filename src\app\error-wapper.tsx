"use client";

import { ErrorBoundary } from "@/layout/components/error-boundary/boundary";
import { ErrorFallback } from "@/layout/components/error-boundary/fallback";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";

const ErrorFallbackHandler = (error: Error) => {
	return <ErrorFallback error={error} resetErrorBoundary={() => window.location.reload()} />;
};

function ErrorWrapper({ children }: IReactChildrenType) {
	return <ErrorBoundary fallback={ErrorFallbackHandler}>{children}</ErrorBoundary>;
}

export default ErrorWrapper;
