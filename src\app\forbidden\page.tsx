import { Home, <PERSON>Alert } from "lucide-react";
import Link from "next/link";
import type { ReactElement } from "react";

export default function ForbiddenPage(): ReactElement {
	return (
		<div
			className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-primary/15 to-background px-4"
			role="alert"
			aria-live="assertive"
		>
			<div className="max-w-2xl w-full p-10 bg-white/90 rounded-2xl shadow-2xl border border-gray-100 backdrop-blur-xl ring-2 ring-primary/20">
				<div className="flex flex-col items-center">
					<span className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-secondary/10 p-6 shadow-xl mb-6 animate-fade-in-forbidden">
						<ShieldAlert className="w-14 h-14 text-primary drop-shadow-lg" />
					</span>
					<h1 className="text-5xl font-extrabold text-transparent bg-clip-text bg-primary mb-2 drop-shadow-xl tracking-tight text-center animate-fade-in-forbidden">
						Não Autorizado
					</h1>
					<h2 className="text-2xl font-semibold text-primary mb-6 drop-shadow text-center animate-fade-in-forbidden delay-100">
						Você não possui permissão para acessar esta página
					</h2>
					<div className="flex flex-wrap gap-4 mb-6 justify-center animate-fade-in-forbidden delay-200">
						<Link
							href="/"
							className="inline-flex rounded-xl items-center gap-2 px-7 py-3 text-base font-bold text-white bg-primary shadow-lg hover:scale-105 hover:shadow-2xl transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-primary/40"
						>
							<Home className="w-5 h-5" />
							Página Inicial
						</Link>
					</div>
					<p className="text-sm text-gray-500 text-center max-w-md animate-fade-in-forbidden delay-300">
						Se você acredita que isso é um erro, entre em contato com{" "}
						<span className="font-semibold text-primary">administrador do sistema.</span>
					</p>
				</div>
			</div>
		</div>
	);
}
