import { AlertOctagon, Home } from "lucide-react";
import Link from "next/link";

const NotFoundPage = () => {
	return (
		<div
			className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-primary/15 to-background px-4 error-page"
			role="alert"
			aria-live="assertive"
		>
			<div className="max-w-xl w-full p-8 bg-background/95 rounded-main shadow-2xl border border-gray-100 backdrop-blur-xl ring-2 ring-primary/20">
				<div className="flex flex-col items-center text-center">
					<span
						className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/10 p-6 shadow-xl mb-6 animate-fade-in"
						aria-hidden="true"
					>
						<AlertOctagon className="w-14 h-14 text-primary drop-shadow-lg" />
					</span>
					<h1 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary mb-2 drop-shadow-xl tracking-tight animate-fade-in">
						Página não encontrada
					</h1>
					<p className="text-base text-[var(--foreground)] mb-6 animate-fade-in delay-100">
						Não foi possível encontrar a página que você está procurando.
					</p>
					<div className="flex flex-wrap gap-4 mb-6 justify-center animate-fade-in delay-300">
						<Link
							href="/"
							className="inline-flex rounded-main items-center gap-2 px-6 py-2.5 text-sm font-bold text-white bg-gradient-to-r from-primary to-primary shadow-lg hover:scale-105 hover:shadow-2xl transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-primary/40"
							aria-label="Voltar para a página inicial"
						>
							<Home className="w-4 h-4" />
							Página Inicial
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
};

export default NotFoundPage;
