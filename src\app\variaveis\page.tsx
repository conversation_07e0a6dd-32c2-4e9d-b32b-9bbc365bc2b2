import { cn } from "@/shared/lib/shadcn/utils";

interface IVariableItemProps {
	className: string;
	label: string;
	description: string;
	category: string;
}

interface IVariableSectionProps {
	title: string;
	description: string;
	children: React.ReactNode;
}

const Variaveis = () => {
	return (
		<div className="min-h-screen bg-background p-8">
			<div className="mx-auto max-w-7xl">
				{/* Header */}
				<div className="mb-12 text-center">
					<h1 className="text-4xl font-bold text-foreground mb-4">Sistema de Design</h1>
					<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
						Variáveis CSS organizadas por categoria para manter consistência visual em toda a aplicação
					</p>
				</div>

				{/* Cores Principais */}
				<VariableSection title="Cores Principais" description="Cores fundamentais para botões, links e elementos de destaque">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<VariableItem
							className="bg-primary"
							label="Primary"
							description="Botões principais, links e elementos de ação"
							category="cores-principais"
						/>
						<VariableItem
							className="bg-secondary"
							label="Secondary"
							description="Botões secundários e elementos de suporte"
							category="cores-principais"
						/>
						<VariableItem
							className="bg-destructive"
							label="Destructive"
							description="Ações perigosas como deletar ou cancelar"
							category="cores-principais"
						/>
					</div>
				</VariableSection>

				{/* Cores de Fundo */}
				<VariableSection title="Cores de Fundo" description="Cores para backgrounds e superfícies">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<VariableItem
							className="bg-background"
							label="Background"
							description="Fundo principal da aplicação"
							category="cores-fundo"
						/>
						<VariableItem
							className="bg-background-secondary"
							label="Background Secondary"
							description="Fundo secundário para cards e seções"
							category="cores-fundo"
						/>
						<VariableItem className="bg-card" label="Card" description="Fundo para cards e containers" category="cores-fundo" />
					</div>
				</VariableSection>

				{/* Cores de Texto */}
				<VariableSection title="Cores de Texto" description="Cores para diferentes níveis de texto">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<VariableItem
							className="bg-text-primary"
							label="Text Primary"
							description="Texto principal e títulos"
							category="cores-texto"
						/>
						<VariableItem
							className="bg-text-secondary"
							label="Text Secondary"
							description="Texto secundário e descrições"
							category="cores-texto"
						/>
						<VariableItem
							className="bg-muted-foreground"
							label="Muted Foreground"
							description="Texto com menor destaque"
							category="cores-texto"
						/>
					</div>
				</VariableSection>

				{/* Cores Específicas */}
				<VariableSection title="Cores Específicas" description="Cores com propósitos específicos">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<VariableItem
							className="bg-leaf-green-color"
							label="Leaf Green"
							description="Cor verde para elementos naturais"
							category="cores-especificas"
						/>
						<VariableItem
							className="bg-accent"
							label="Accent"
							description="Cor de destaque para elementos especiais"
							category="cores-especificas"
						/>
						<VariableItem className="bg-border" label="Border" description="Cor para bordas e divisores" category="cores-especificas" />
					</div>
				</VariableSection>

				{/* Cores de Chart */}
				<VariableSection title="Cores de Gráficos" description="Paleta de cores para visualizações e gráficos">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
						<VariableItem
							className="bg-chart-1"
							label="Chart 1"
							description="Primeira cor da paleta de gráficos"
							category="cores-chart"
						/>
						<VariableItem className="bg-chart-2" label="Chart 2" description="Segunda cor da paleta de gráficos" category="cores-chart" />
						<VariableItem
							className="bg-chart-3"
							label="Chart 3"
							description="Terceira cor da paleta de gráficos"
							category="cores-chart"
						/>
						<VariableItem className="bg-chart-4" label="Chart 4" description="Quarta cor da paleta de gráficos" category="cores-chart" />
						<VariableItem className="bg-chart-5" label="Chart 5" description="Quinta cor da paleta de gráficos" category="cores-chart" />
					</div>
				</VariableSection>

				{/* Border Radius */}
				<VariableSection title="Border Radius" description="Valores de raio para bordas arredondadas">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<RadiusVariable
							className="rounded-main"
							label="Main Radius"
							description="Raio principal para cards e containers"
							category="radius"
						/>
						<RadiusVariable
							className="rounded-controls"
							label="Controls Radius"
							description="Raio para botões e controles"
							category="radius"
						/>
						<RadiusVariable
							className="rounded-lg"
							label="Large Radius"
							description="Raio grande para elementos destacados"
							category="radius"
						/>
					</div>
				</VariableSection>

				{/* Spacing */}
				<VariableSection title="Espaçamentos" description="Valores de padding e margin padronizados">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						<SpacingVariable size="p-1" label="Extra Small" description="4px - Espaçamento mínimo" category="spacing" />
						<SpacingVariable size="p-2" label="Small" description="8px - Espaçamento pequeno" category="spacing" />
						<SpacingVariable size="p-4" label="Medium" description="16px - Espaçamento médio" category="spacing" />
						<SpacingVariable size="p-6" label="Large" description="24px - Espaçamento grande" category="spacing" />
					</div>
				</VariableSection>
			</div>
		</div>
	);
};

const VariableSection = ({ title, description, children }: IVariableSectionProps) => {
	return (
		<section className="mb-16">
			<div className="mb-8">
				<h2 className="text-2xl font-semibold text-foreground mb-2">{title}</h2>
				<p className="text-muted-foreground">{description}</p>
			</div>
			{children}
		</section>
	);
};

const VariableItem = ({ className, label, description }: IVariableItemProps) => {
	return (
		<div className="group relative bg-card border border-border rounded-lg p-6 hover:shadow-lg transition-all duration-200">
			<div className="flex items-center gap-4 mb-4">
				<div className={cn("w-16 h-16 rounded-lg shadow-md border-2 border-border", className)}></div>
				<div className="flex-1">
					<h3 className="font-medium text-foreground">{label}</h3>
					<p className="text-sm text-muted-foreground">{description}</p>
				</div>
			</div>
			<div className="bg-muted rounded-md p-3">
				<code className="text-xs text-muted-foreground font-mono">{className}</code>
			</div>
		</div>
	);
};

const RadiusVariable = ({ className, label, description }: IVariableItemProps) => {
	return (
		<div className="group relative bg-card border border-border rounded-lg p-6 hover:shadow-lg transition-all duration-200">
			<div className="flex items-center gap-4 mb-4">
				<div className={cn("w-16 h-16 bg-primary shadow-md border-2 border-border", className)}></div>
				<div className="flex-1">
					<h3 className="font-medium text-foreground">{label}</h3>
					<p className="text-sm text-muted-foreground">{description}</p>
				</div>
			</div>
			<div className="bg-muted rounded-md p-3">
				<code className="text-xs text-muted-foreground font-mono">{className}</code>
			</div>
		</div>
	);
};

const SpacingVariable = ({ size, label, description }: { size: string; label: string; description: string; category: string }) => {
	return (
		<div className="group relative bg-card border border-border rounded-lg p-6 hover:shadow-lg transition-all duration-200">
			<div className="flex items-center gap-4 mb-4">
				<div className={cn("w-16 h-16 bg-secondary border-2 border-border flex items-center justify-center", size)}>
					<div className="w-4 h-4 bg-primary rounded-sm"></div>
				</div>
				<div className="flex-1">
					<h3 className="font-medium text-foreground">{label}</h3>
					<p className="text-sm text-muted-foreground">{description}</p>
				</div>
			</div>
			<div className="bg-muted rounded-md p-3">
				<code className="text-xs text-muted-foreground font-mono">{size}</code>
			</div>
		</div>
	);
};

export default Variaveis;
