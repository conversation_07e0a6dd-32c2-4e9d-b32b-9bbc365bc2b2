import { AccessTokenValidator } from "@/config/services/access-token-validator";
import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;
	const accessTokenValidator = new AccessTokenValidator();
	const validationResult = await accessTokenValidator.validate(request);

	if (!validationResult.isValid || validationResult.status !== "ok") {
		const loginUrl = new URL("/login", request.url);
		loginUrl.searchParams.set("redirect", pathname);
		return NextResponse.rewrite(loginUrl);
	}

	return NextResponse.next();
}
