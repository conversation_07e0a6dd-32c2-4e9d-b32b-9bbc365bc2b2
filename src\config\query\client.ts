import { QueryClient } from "@tanstack/react-query";

export const createQueryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchOnWindowFocus: false,
			refetchOnMount: false,
			refetchOnReconnect: false,
			refetchInterval: false,
			refetchIntervalInBackground: false,
			retry: false,
			staleTime: 30 * 60 * 1000,
			gcTime: 60 * 60 * 1000,
		},
		mutations: {
			retry: false,
		},
	},
});
