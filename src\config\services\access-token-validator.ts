import { NextRequest } from "next/server";

import { IValidateTokenSignatureReturn, TokenValidator, validateTokenSignature } from "./validate-token-signature";
import { COOKIE_NAMES } from "../cookies/name";

export class AccessTokenValidator implements TokenValidator {
	async validate(request: NextRequest): Promise<IValidateTokenSignatureReturn> {
		return await validateTokenSignature(request, COOKIE_NAMES.ACCESS_TOKEN);
	}
}
