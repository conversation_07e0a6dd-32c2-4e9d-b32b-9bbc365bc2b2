import { NextRequest } from "next/server";
import { IValidateTokenSignatureReturn, TokenValidator } from "./validate-token-signature";

export class CascadeTokenValidator implements TokenValidator {
	constructor(private readonly validators: TokenValidator[]) {}

	async validate(request: NextRequest): Promise<IValidateTokenSignatureReturn> {
		for (const validator of this.validators) {
			const result = await validator.validate(request);
			if (result.isValid && result.status === "ok") return result;
		}
		return {
			isValid: false,
			status: "unauthorized",
			message: "Nenhum token válido encontrado",
		};
	}
}
