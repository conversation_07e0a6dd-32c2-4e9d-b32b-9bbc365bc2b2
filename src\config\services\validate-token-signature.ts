import { validateSignature } from "@/shared/lib/cookies/signature/config";
import { getCookieName } from "@/shared/lib/cookies/utils/get-cookie-name";
import { NextRequest } from "next/server";

export interface IValidateTokenSignatureReturn {
	isValid: boolean;
	status: "ok" | "unauthorized";
	message?: string;
}

export interface TokenValidator {
	validate(request: NextRequest): Promise<IValidateTokenSignatureReturn>;
}

const unauthorized = (message: string): IValidateTokenSignatureReturn => ({
	isValid: false,
	status: "unauthorized",
	message,
});

const authorized = (isValid: boolean): IValidateTokenSignatureReturn => ({
	isValid,
	status: isValid ? "ok" : "unauthorized",
	message: isValid ? undefined : "Ops! Parece que esse token possui uma assinatura inválida.",
});

export const validateTokenSignature = async (request: NextRequest, tokenName: string): Promise<IValidateTokenSignatureReturn> => {
	if (!tokenName || typeof tokenName !== "string") return unauthorized("Token inválido.");
	const cookieValue = request.cookies.get(getCookieName(tokenName))?.value;
	if (!cookieValue) return unauthorized("Cookie não encontrado ou inválido.");
	try {
		const { isValid } = await validateSignature(cookieValue);
		return authorized(isValid);
	} catch {
		return unauthorized("Erro ao validar o token.");
	}
};
