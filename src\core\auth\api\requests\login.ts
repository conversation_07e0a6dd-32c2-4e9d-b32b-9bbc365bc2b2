import { createPostRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { AUTH_ENDPOINTS } from "../endpoints";
import { setAuthTokens } from "../../lib/auth-actions";

export interface ILoginResponse {
	access_token: string;
}

export const loginRequest = async (username: string, password: string): Promise<ApiResponse<ILoginResponse>> => {
	return createPostRequest<ILoginResponse>(AUTH_ENDPOINTS.LOGIN, {
		username,
		password,
	});
};

export const loginEntry = async (username: string, password: string): Promise<ApiResponse<boolean>> => {
	const response = await loginRequest(username, password);
	if (!response.success) return response;
	await setAuthTokens(response.data.access_token);
	return {
		...response,
		data: true,
	};
};
