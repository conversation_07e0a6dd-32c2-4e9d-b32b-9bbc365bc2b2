import { atom } from "jotai";
import { IUser } from "../types/user.types";

export const userAtom = atom<IUser | null>(null);

export const userNamePartsAtom = atom(get => {
	const user = get(userAtom);
	if (!user?.name) return { firstName: null, lastName: null };

	const [firstName, ...lastNameParts] = user.name.split(" ");
	return {
		firstName,
		lastName: lastNameParts.join(" ") || null,
	};
});

export const userNameAtom = atom(get => {
	const user = get(userAtom);
	return user?.name ?? null;
});

export const userPermissionsAtom = atom(get => {
	const user = get(userAtom);
	return user?.permissions ?? [];
});

export const getUserInitials = atom(get => {
	const userName = get(userNameAtom);
	if (!userName) return "U";
	const nameParts = userName.split(" ");
	if (nameParts.length === 1) return nameParts[0].substring(0, 2).toUpperCase();
	return (nameParts[0][0] + (nameParts[nameParts.length - 1]?.[0] || "")).toUpperCase();
});
