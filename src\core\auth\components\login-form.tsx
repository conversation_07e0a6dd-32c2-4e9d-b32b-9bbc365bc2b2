"use client";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { useLoginForm } from "../hooks/login/login-form.hook";
import { useLoginMutation } from "../hooks/login/login-mutation.hook";
import { Input } from "@/shared/components/shadcn/input";
import { Button } from "@/shared/components/shadcn/button";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";

export const LoginForm = () => {
    const { login, isLoading } = useLoginMutation();
    const methods = useLoginForm();
    const [visiblePassword, setVisiblePassword] = useState(false);

    return (
        <Form {...methods}>
            <form onSubmit={methods.handleSubmit(login)} className="flex flex-col items-center gap-6 w-full px-4">
                <FormField
                    control={methods.control}
                    name="username"
                    render={({ field }) => (
                        <FormItem className="w-full max-w-[21.62rem]">
                            <FormLabel className="font-normal text-md">Usuário</FormLabel>
                            <FormControl>
                                <Input placeholder="Usuário" {...field} className="bg-[#ffffff] border border-[#d1d5db] h-[2.87rem] " />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <FormField
                    control={methods.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem className="w-full max-w-[21.62rem]">
                            <FormLabel className="font-normal text-md">Senha</FormLabel>
                            <FormControl className="relative">
                                <div className="relative w-full max-w-[21.62rem]">
                                    <Input
                                        type={visiblePassword ? "text" : "password"}
                                        placeholder="Senha"
                                        {...field}
                                        className="bg-white border border-gray-300 h-[2.875rem] w-full pr-10"
                                    />
                                    <Button
                                        type="button"
                                        size="icon"
                                        className="absolute right-[1rem] top-1/2 -translate-y-1/2 bg-transparent text-gray-500 cursor-pointer hover:bg-transparent"
                                        onClick={() => setVisiblePassword((prev) => !prev)}
                                    >
                                        {visiblePassword ? (
                                            <Eye className="w-[1.25rem] h-[1.25rem]" />
                                        ) : (
                                            <EyeOff className="w-[1.25rem] h-[1.25rem]" />
                                        )}
                                    </Button>
                                </div>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <Button
                    type="submit"
                    className="self-center h-[2.87rem] font-normal text-md  cursor-pointer w-full max-w-[21.62rem] mt-4.5 rounded-control"
                >
                    {isLoading ? "Carregando..." : "Entrar"}
                </Button>
            </form>
        </Form>
    );
};
