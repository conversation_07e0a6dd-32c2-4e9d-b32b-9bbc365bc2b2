"use client";

import { useAtomValue } from "jotai";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom, userNameAtom, userPermissionsAtom } from "../../atoms/user.atom";
import { IUser } from "../../types/user.types";
import { IRole } from "@/config/enums/role.enum";

export const useUser = () => {
	const user = useAtomValue<IUser | null>(userAtom);
	const userName = useAtomValue(userNameAtom);
	const permissions = useAtomValue(userPermissionsAtom);
	const isAuthenticated = useAtomValue(isAuthenticatedAtom);

	return {
		user,
		userName,
		permissions,
		isAuthenticated,
		hasPermission: (permission: IRole) => permissions.includes(permission),
	};
};
