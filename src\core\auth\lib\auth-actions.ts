"use server";

import { COOKIE_NAMES } from "@/config/cookies/name";
import { createCookie } from "@/shared/lib/cookies/crud/create";
import { getCookie } from "@/shared/lib/cookies/crud/get";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { getJWTMaxAge } from "@/shared/lib/jwt/max-age";
import { ApiResponse } from "@/shared/types/requests/request.type";

export const clearAuthTokens = async (): Promise<ApiResponse<{ message: string }>> => {
	const result = await removeCookie({ name: COOKIE_NAMES.ACCESS_TOKEN });
	if (!result.success) {
		return {
			success: false,
			data: { message: "Erro ao remover token" },
			status: 500,
		};
	}
	return {
		success: true,
		data: { message: "Token removido com sucesso" },
		status: 200,
	};
};

export const getAuthToken = async (): Promise<string | null> => {
	const { success, value } = await getCookie({
		name: COOKIE_NAMES.ACCESS_TOKEN,
	});
	return success ? value : null;
};

export const setAuthTokens = async (accessToken: string): Promise<ApiResponse<boolean>> => {
	const setCookie = async (name: string, token: string) => {
		const maxAge = getJWTMaxAge(token) ?? 0;
		return createCookie({
			name,
			value: token,
			options: { secure: true, sameSite: "strict", maxAge },
		});
	};

	const accessRes = await setCookie(COOKIE_NAMES.ACCESS_TOKEN, accessToken);
	if (!accessRes.success) return accessRes;

	return { success: true, data: true, status: 200 };
};
