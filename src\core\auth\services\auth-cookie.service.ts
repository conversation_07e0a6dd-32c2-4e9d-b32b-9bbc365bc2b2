import * as AuthActions from "../lib/auth-actions";

export class AuthCookieManager {
	static readonly setAuthTokens = AuthActions.setAuthTokens;
	public static readonly getAuthToken = AuthActions.getAuthToken;
	public static readonly clearTokens = AuthActions.clearAuthTokens;

	static async getAllTokens() {
		const accessToken = await this.getAuthToken();
		return { accessToken };
	}

	static async isAuthenticated() {
		return (await this.getAuthToken()) !== null;
	}
}
