import { AxiosInstance, AxiosResponse, AxiosRequestConfig as OriginalAxiosRequestConfig } from "axios";
import { AUTH_ENDPOINTS } from "../api/endpoints";

import { clearAuthTokens, getAuthToken } from "../lib/auth-actions";
import { refreshRequest } from "../api/requests/refresh";

interface AxiosRequestConfig extends OriginalAxiosRequestConfig {
	_retry?: boolean;
}

export const REFRESH_ERROR_MESSAGE = "O token de acesso expirou. Por favor, faça login novamente.";

class RefreshService {
	private isRefreshing = false;
	private refreshSubscribers: Array<(token: string) => void> = [];
	private readonly nonRefreshableEndpoints = [AUTH_ENDPOINTS.LOGIN, AUTH_ENDPOINTS.REFRESH, AUTH_ENDPOINTS.LOGOUT];

	constructor(private readonly axiosInstance: AxiosInstance) {}

	public isNonRefreshableEndpoint(url: string): boolean {
		return this.nonRefreshableEndpoints.some(endpoint => url.includes(endpoint));
	}

	private resetRefreshState(): void {
		this.isRefreshing = false;
		this.refreshSubscribers = [];
	}

	private notifySubscribers(token: string): void {
		this.refreshSubscribers.forEach(cb => cb(token));
		this.resetRefreshState();
	}

	public async handleTokenRefresh(originalRequest: AxiosRequestConfig): Promise<AxiosResponse> {
		if (this.isRefreshing) {
			return new Promise(resolve => {
				this.refreshSubscribers.push(token => {
					originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${token}` };
					resolve(this.axiosInstance(originalRequest));
				});
			});
		}

		this.isRefreshing = true;
		originalRequest._retry = true;

		try {
			const accessToken = await getAuthToken();
			if (!accessToken) {
				await clearAuthTokens();
				this.resetRefreshState();
				return Promise.reject(new Error("Token de acesso não encontrado."));
			}

			const refreshResult = await refreshRequest({ token: accessToken });

			if (!refreshResult.success) {
				await clearAuthTokens();
				this.notifySubscribers(REFRESH_ERROR_MESSAGE);
				throw new Error(REFRESH_ERROR_MESSAGE);
			}

			const { access_token } = refreshResult.data;
			this.notifySubscribers(access_token);
			originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${access_token}` };
			return this.axiosInstance(originalRequest);
		} catch (error) {
			await clearAuthTokens();
			this.resetRefreshState();
			if (error instanceof Error) {
				throw error;
			}
			throw new Error(String(error));
		}
	}
}

export default RefreshService;
