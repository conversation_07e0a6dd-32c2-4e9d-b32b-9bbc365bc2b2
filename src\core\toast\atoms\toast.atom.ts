import { Atom, atom, WritableAtom } from "jotai";
import { ToastPosition, ToastProps } from "../types/toast.type";

export const toastPositionAtom = atom<ToastPosition>("top-right");
export const maxToastsAtom = atom<number>(3);

export const toastsAtom = atom<ToastProps[]>([]);
export const toastTimeoutsAtom = atom<Record<string, NodeJS.Timeout>>({});

const manageToastTimeout = (
	id: string,
	duration: number,
	set: <T>(atom: WritableAtom<T, [T | ((prev: T) => T)], void>, updater: T | ((prev: T) => T)) => void,
	get: <T>(atom: Atom<T>) => T
) => {
	const existingTimeouts = get<Record<string, NodeJS.Timeout>>(toastTimeoutsAtom);
	if (existingTimeouts[id]) clearTimeout(existingTimeouts[id]);

	const timeout = setTimeout(() => {
		set(toastsAtom, prev => prev.filter(t => t.id !== id));
		set(toastTimeoutsAtom, (prev: Record<string, NodeJS.Timeout>) => {
			const newTimeouts = { ...prev };
			delete newTimeouts[id];
			return newTimeouts;
		});
	}, duration);

	set(toastTimeoutsAtom, { ...existingTimeouts, [id]: timeout });

	return timeout;
};

const removeExcessToasts = (
	updatedToasts: ToastProps[],
	maxToasts: number,
	newToast: ToastProps,
	get: <T>(atom: Atom<T>) => T,
	set: <T>(atom: WritableAtom<T, [T | ((prev: T) => T)], void>, updater: T | ((prev: T) => T)) => void
) => {
	if (updatedToasts.length <= maxToasts) return updatedToasts;

	const priorities = ["low", "normal", "high"];
	let toRemove = updatedToasts.length - maxToasts;
	let result = [...updatedToasts];

	priorities.forEach(priority => {
		if (toRemove <= 0) return;

		const candidatesToRemove = result.filter(t => t.id !== newToast.id && t.priority === priority).slice(0, toRemove);

		if (candidatesToRemove.length > 0) {
			candidatesToRemove.forEach(t => {
				const timeouts = get(toastTimeoutsAtom);
				if (timeouts[t.id]) {
					clearTimeout(timeouts[t.id]);
					set(toastTimeoutsAtom, prev => {
						const newTimeouts = { ...prev };
						delete newTimeouts[t.id];
						return newTimeouts;
					});
				}
			});

			const idsToRemove = new Set(candidatesToRemove.map(t => t.id));
			result = result.filter(t => !idsToRemove.has(t.id));
			toRemove -= candidatesToRemove.length;
		}
	});

	return result;
};

export const addToastAtom = atom(null, (get, set, toast: Omit<ToastProps, "id">) => {
	const id = Math.random().toString(36).substring(2, 9);
	const newToast: ToastProps = {
		...toast,
		id,
		duration: toast.duration ?? 5000,
		priority: toast.priority ?? "normal",
		dismissable: toast.dismissable !== false,
		pauseOnHover: toast.pauseOnHover !== false,
	};

	const currentToasts = get(toastsAtom);
	const maxToasts = get(maxToastsAtom);

	let updatedToasts = [...currentToasts];

	if (newToast.origin) {
		const existingToastIndex = updatedToasts.findIndex(t => t.origin === newToast.origin);
		if (existingToastIndex !== -1) {
			const existingToast = updatedToasts[existingToastIndex];
			const timeouts = get(toastTimeoutsAtom);
			if (timeouts[existingToast.id]) {
				clearTimeout(timeouts[existingToast.id]);
				set(toastTimeoutsAtom, prev => {
					const newTimeouts = { ...prev };
					delete newTimeouts[existingToast.id];
					return newTimeouts;
				});
			}
			updatedToasts.splice(existingToastIndex, 1);
		}
	}

	updatedToasts = [...updatedToasts, newToast];
	updatedToasts = removeExcessToasts(updatedToasts, maxToasts, newToast, get, set);

	set(toastsAtom, updatedToasts);
	if (newToast.duration !== Infinity) manageToastTimeout(id, newToast.duration!, set, get);

	return id;
});

export const pauseToastTimerAtom = atom(null, (get, set, id: string) => {
	const timeouts = get(toastTimeoutsAtom);
	if (timeouts[id]) {
		clearTimeout(timeouts[id]);
		set(toastTimeoutsAtom, prev => {
			const newTimeouts = { ...prev };
			delete newTimeouts[id];
			return newTimeouts;
		});
	}
});

export const resumeToastTimerAtom = atom(null, (get, set, { id, duration }: { id: string; duration: number }) => {
	manageToastTimeout(id, duration, set, get);
});

export const removeToastAtom = atom(null, (get, set, id: string) => {
	const timeouts = get(toastTimeoutsAtom);
	if (timeouts[id]) clearTimeout(timeouts[id]);

	set(toastTimeoutsAtom, prev => {
		const newTimeouts = { ...prev };
		delete newTimeouts[id];
		return newTimeouts;
	});

	set(toastsAtom, prev => prev.filter(toast => toast.id !== id));
});

export const updateToastConfigAtom = atom(null, (get, set, config: { position?: ToastPosition; maxToasts?: number }) => {
	if (config.position) set(toastPositionAtom, config.position);
	if (config.maxToasts) set(maxToastsAtom, config.maxToasts);
});
