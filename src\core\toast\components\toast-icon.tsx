"use client";
import { motion } from "framer-motion";
import React from "react";

interface ToastIconProps {
	type: "success" | "error" | "warning" | "loading" | "info";
}

export const ToastIcon: React.FC<ToastIconProps> = ({ type }) => {
	switch (type) {
		case "success":
			return (
				<motion.path
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={2}
					d="M5 13l4 4L19 7"
					initial={{ pathLength: 0 }}
					animate={{ pathLength: 1 }}
					transition={{ duration: 0.5, ease: "easeInOut" }}
				/>
			);
		case "error":
			return (
				<motion.path
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={2}
					d="M6 18L18 6M6 6l12 12"
					initial={{ pathLength: 0 }}
					animate={{ pathLength: 1 }}
					transition={{ duration: 0.5, ease: "easeInOut" }}
				/>
			);
		case "warning":
			return (
				<motion.path
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={2}
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
					initial={{ pathLength: 0 }}
					animate={{ pathLength: 1 }}
					transition={{ duration: 0.5, ease: "easeInOut" }}
				/>
			);
		case "loading":
			return (
				<motion.path
					strokeLinecap="round"
					strokeLinejoin="round"
					strokeWidth={2}
					d="M12 3a9 9 0 11-9 9"
					animate={{
						rotate: 360,
					}}
					transition={{
						duration: 1,
						repeat: Infinity,
						ease: "linear",
						repeatType: "loop",
					}}
				/>
			);
		case "info":
		default:
			return (
				<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
			);
	}
};
