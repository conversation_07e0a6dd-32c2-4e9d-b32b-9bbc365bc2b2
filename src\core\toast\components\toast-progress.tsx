"use client";
import { motion } from "framer-motion";
import React from "react";

interface ToastProgressProps {
	progress: number;
	styleClass: string;
	isInfinite: boolean;
}

export const ToastProgress: React.FC<ToastProgressProps> = ({ progress, styleClass, isInfinite }) => {
	if (isInfinite) return null;

	return (
		<motion.div
			className={`absolute bottom-0 left-0 h-0.5 ${styleClass}`}
			style={{ width: `${progress}%` }}
			initial={{ opacity: 0.3 }}
			animate={{ opacity: 0.7 }}
		/>
	);
};
