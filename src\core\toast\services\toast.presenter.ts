import React from "react";

import { IToastPresenter, IToastStore, ToastOptions, ToastType } from "../types/toast.type";

export class ToastPresenter implements IToastPresenter {
	constructor(private readonly store: IToastStore) {}

	success(message: string, options?: ToastOptions): string {
		return this.store.addToast(message, "success", options);
	}

	error(message: string | string[], options?: ToastOptions): string {
		const formattedMessage = Array.isArray(message) ? message.map(item => `• ${item}`).join("\n") : message;
		return this.store.addToast(formattedMessage, "error", options);
	}

	warning(message: string, options?: ToastOptions): string {
		return this.store.addToast(message, "warning", options);
	}

	info(message: string, options?: ToastOptions): string {
		return this.store.addToast(message, "info", options);
	}

	loading(message: string, options?: ToastOptions): string {
		return this.store.addToast(message, "loading", { ...options, duration: Infinity });
	}

	custom(customContent: React.ReactNode, type: ToastType = "info", options?: ToastOptions): string {
		return this.store.addToast("", type, { ...options, customContent });
	}

	async promise<T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success?: string;
			error?: string;
		},
		options?: ToastOptions
	): Promise<T> {
		const toastId = this.loading(messages.loading, options);

		try {
			const result = await promise;
			if (messages.success) {
				this.store.removeToast(toastId);
				this.success(messages.success, options);
			} else {
				this.store.removeToast(toastId);
			}
			return result;
		} catch (error) {
			this.store.removeToast(toastId);
			this.error(messages.error ?? (error as Error).message, options);
			throw error;
		}
	}
}
