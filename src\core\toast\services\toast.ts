import React from "react";

import { IToastService, ToastConfiguration, ToastOptions, ToastType } from "../types/toast.type";
import { ToastPresenter } from "./toast.presenter";
import { JotaiToastStore } from "./toast.store";

class ToastService implements IToastService {
	private readonly store: JotaiToastStore;
	private readonly presenter: ToastPresenter;

	constructor() {
		this.store = new JotaiToastStore();
		this.presenter = new ToastPresenter(this.store);
	}

	success(message: string, options?: ToastOptions): string {
		return this.presenter.success(message, options);
	}

	error(message: string | string[], options?: ToastOptions): string {
		return this.presenter.error(message, options);
	}

	warning(message: string, options?: ToastOptions): string {
		return this.presenter.warning(message, options);
	}

	info(message: string, options?: ToastOptions): string {
		return this.presenter.info(message, options);
	}

	loading(message: string, options?: ToastOptions): string {
		return this.presenter.loading(message, options);
	}

	custom(customContent: React.ReactNode, type: ToastType = "info", options?: ToastOptions): string {
		return this.presenter.custom(customContent, type, options);
	}

	dismiss(id: string): void {
		this.store.removeToast(id);
	}

	pauseTimer(id: string): void {
		this.store.pauseToastTimer(id);
	}

	resumeTimer(id: string, remainingTime: number): void {
		this.store.resumeToastTimer(id, remainingTime);
	}

	async promise<T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success?: string;
			error?: string;
		},
		options?: ToastOptions
	): Promise<T> {
		return this.presenter.promise(promise, messages, options);
	}

	configure(config: Partial<ToastConfiguration>): void {
		this.store.updateToastConfig(config);
	}

	// 	update(id: string, message: string, options?: Partial<ToastOptions>): void {
	// 		this.store.updateToast(id, message, options);
	// 	}

	// 	clearAll(types?: ToastType[]): void {
	// 		this.store.clearAllToasts(types);
	// 	}
	// }
}
// Export a singleton instance
export const toast = new ToastService();
