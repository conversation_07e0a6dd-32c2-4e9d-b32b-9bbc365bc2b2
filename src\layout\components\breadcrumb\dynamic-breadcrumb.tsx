"use client";

import { useDynamicBreadcrumb } from "@/layout/hooks/dynamic-breadcrumb.hook";
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from "@/shared/components/shadcn/breadcrumb";
import Link from "next/link";
import { Fragment, JSX, type FC } from "react";

export const DynamicBreadcrumb: FC = (): JSX.Element | undefined => {
	const { uniqueBreadcrumbPath } = useDynamicBreadcrumb();
	const lastItem = uniqueBreadcrumbPath[uniqueBreadcrumbPath.length - 1];

	return (
		<div className="flex flex-col">
			<Breadcrumb>
				<BreadcrumbList>
					{uniqueBreadcrumbPath.map((item, idx) => (
						<Fragment key={`${item.label}-${idx}`}>
							<BreadcrumbItem>
								{idx === uniqueBreadcrumbPath.length - 1 ? (
									<BreadcrumbPage className="font-semibold text-text-primary text-[15px]">{item.label}</BreadcrumbPage>
								) : (
									<>
										{item.isActive ? (
											<BreadcrumbLink className="text-text-secondary font-light text-[15px]" asChild>
												<Link href={item.href ?? "#"}>{item.label}</Link>
											</BreadcrumbLink>
										) : (
											<span className="text-text-secondary font-light text-[15px] ransition-colors hover:text-foreground">
												{item.label}
											</span>
										)}
									</>
								)}
							</BreadcrumbItem>
							{idx < uniqueBreadcrumbPath.length - 1 && <BreadcrumbSeparator className="text-[15px] text-text-secondary" />}
						</Fragment>
					))}
				</BreadcrumbList>
			</Breadcrumb>
			{lastItem.description && <p className="text-base text-text-secondary">{lastItem.description}</p>}
		</div>
	);
};
