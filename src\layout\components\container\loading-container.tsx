import type { FC, JSX } from "react";

type LoadingSpinnerProps = Readonly<{
	size?: number;
	className?: string;
}>;

export const LoadingSpinner: FC<LoadingSpinnerProps> = ({ size = 64, className = "" }): JSX.Element => (
	<div
		className={`border-4 border-dashed rounded-full animate-spin border-primary ${className}`}
		style={{ width: size, height: size }}
		aria-label="Loading"
		role="status"
	/>
);

type LoadingContainerProps = Readonly<{
	spinnerSize?: number;
	className?: string;
}>;

export const LoadingContainer: FC<LoadingContainerProps> = ({ spinnerSize, className = "" }): JSX.Element => (
	<div className={`flex justify-center flex-1 items-center h-screen  bg-gradient-to-br from-primary/15 to-background ${className}`}>
		<LoadingSpinner size={spinnerSize} />
	</div>
);
