"use client";
import { SidebarInset, SidebarProvider } from "@/shared/components/shadcn/sidebar";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { AppSidebar } from "./app.sidebar";

export const MainContainer = ({ children }: IReactChildrenType) => {
	return (
		<main className="relative flex min-h-dvh max-h-screen w-full overflow-hidden sm:min-h-screen">
			<SidebarProvider>
				<AppSidebar />
				<SidebarInset className="flex-1 overflow-y-auto min-w-0">
					<span className="flex h-5 shrink-0 items-center bg-background  justify-between" />
					<div className="flex bg-background-secondary rounded-tl-main  p-6 flex-1 flex-col">{children}</div>
				</SidebarInset>
			</SidebarProvider>
		</main>
	);
};
