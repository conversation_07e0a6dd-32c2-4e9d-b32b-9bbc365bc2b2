"use client";

import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { Component, ReactNode } from "react";

interface IErrorBoundaryProps extends IReactChildrenType {
	fallback?: ReactNode | ((error: Error) => ReactNode);
	onError?: (error: Error, info: React.ErrorInfo) => void;
}

interface ICreateStateErrorBoundary {
	hasError: boolean;
	error: Error | null;
}

export class ErrorBoundary extends Component<IErrorBoundaryProps, ICreateStateErrorBoundary> {
	constructor(props: IErrorBoundaryProps) {
		super(props);
		this.state = {
			hasError: false,
			error: null,
		};
	}

	static getDerivedStateFromError(error: Error): ICreateStateErrorBoundary {
		return {
			hasError: true,
			error,
		};
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		if (this.props.onError) {
			this.props.onError(error, errorInfo);
		}
	}

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				if (typeof this.props.fallback === "function") {
					return this.props.fallback(this.state.error!);
				}
				return this.props.fallback;
			}

			return (
				<div className="error-boundary-fallback">
					<h2>Algo deu errado</h2>
					<details>
						<summary>Detalhes do erro</summary>
						<pre>{this.state.error?.message}</pre>
					</details>
				</div>
			);
		}

		return this.props.children;
	}
}
