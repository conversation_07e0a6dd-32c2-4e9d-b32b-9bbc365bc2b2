"use client";

import { AlertTriangle, Home, RefreshCw } from "lucide-react";
import Link from "next/link";
import { ReactElement, useMemo } from "react";
import { AxiosError } from "axios";
import { handleGlobalError } from "@/shared/lib/errors";

type TErrorFallbackProps = Readonly<{
	error: Error;
	resetErrorBoundary: () => void;
}>;

export const ErrorFallback = ({ error, resetErrorBoundary }: TErrorFallbackProps): ReactElement => {
	const isAxiosError = error instanceof AxiosError;
	const errorDetails = useMemo(() => {
		if (isAxiosError) {
			const handledError = handleGlobalError(error);
			return handledError.data;
		}
		return { message: error.message || "Um erro desconhecido ocorreu." };
	}, [error, isAxiosError]);

	return (
		<div
			className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-primary/15 to-background px-4"
			role="alert"
			aria-live="assertive"
		>
			<div className="max-w-xl w-full p-8 bg-background/95 rounded-main shadow-2xl border border-gray-100 backdrop-blur-xl ring-2 ring-primary/20">
				<div className="flex flex-col items-center text-center">
					<span
						className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/10 p-6 shadow-xl mb-6 animate-pulse"
						aria-hidden="true"
					>
						<AlertTriangle className="w-14 h-14 text-primary drop-shadow-lg" />
					</span>
					<h1 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary mb-2 drop-shadow-xl tracking-tight">
						Ocorreu um erro inesperado
					</h1>
					<p className="text-base text-[var(--foreground)] mb-6">{errorDetails.message}</p>
					<details className="w-full max-w-md mb-6 text-text-light rounded-main p-3 text-xs border border-text-light/20 overflow-hidden [&[open]]:block [&[open]>summary]:mb-2">
						<summary className="cursor-pointer font-semibold text-primary hover:text-primary transition-colors">
							Detalhes técnicos
						</summary>
						<pre className="whitespace-pre-wrap break-all mt-2 text-left overflow-x-auto max-h-60 overflow-y-auto">
							{error.stack ?? "Sem detalhes adicionais disponíveis."}
						</pre>
					</details>
					<div className="flex flex-wrap gap-4 mb-6 justify-center">
						{resetErrorBoundary && (
							<button
								type="button"
								onClick={resetErrorBoundary}
								className="inline-flex rounded-main items-center gap-2 px-6 py-2.5 text-sm font-bold text-white bg-gradient-to-r from-primary to-primary shadow-lg hover:scale-105 hover:shadow-2xl transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-primary/40"
								aria-label="Tentar novamente"
							>
								<RefreshCw className="w-4 h-4" />
								Tentar novamente
							</button>
						)}
						<Link
							href="/"
							className="inline-flex rounded-main items-center gap-2 px-6 py-2.5 text-sm font-bold text-white bg-gradient-to-r from-primary to-primary shadow-lg hover:scale-105 hover:shadow-2xl transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-primary/40"
							aria-label="Voltar para a página inicial"
						>
							<Home className="w-4 h-4" />
							Página Inicial
						</Link>
					</div>

					<div className="w-full flex flex-col items-center">
						<p className="text-sm text-text max-w-md mb-2">
							Se o problema persistir, por favor, entre em contato com o{" "}
							<span className="font-semibold text-primary">administrador do sistema</span>.
						</p>
						<p className="text-xs text-text-light max-w-md">
							Informe o horário do erro e, se possível, uma breve descrição do que estava fazendo. Isso ajudará a resolver o problema
							mais rapidamente.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};
