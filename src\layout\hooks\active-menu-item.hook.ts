"use client";

import { pathItems } from "@/config/path-manager/items";
import { pathService } from "@/config/path-manager/service";
import { ICreateBaseItemPathManager } from "@/config/path-manager/types";
import { useNavigatePaths } from "@/shared/hooks/utils";
import { useCallback, useEffect, useMemo, useState } from "react";

interface IActiveMenuState {
	activeItemId: string | null;
	activeSubItemId: string | null;
	parentOfActiveSubItemId: string | null;
}

interface IUseActiveMenuItemReturn {
	activeItemId: string | null;
	activeSubItemId: string | null;
	parentOfActiveSubItemId: string | null;
	isMenuItemActive: (id: string) => boolean;
	isSubMenuItemActive: (id: string) => boolean;
	updateActiveItems: () => void;
}

const createInitialState = (): IActiveMenuState => ({
	activeItemId: null,
	activeSubItemId: null,
	parentOfActiveSubItemId: null,
});

const findClosestVisibleMenuItem = (path: string): ICreateBaseItemPathManager | null => {
	let item = pathService.getItemByPath(path);
	if (item?.visibleOnMenu) return item;
	const segments = path.split("/").filter(Boolean);
	while (segments.length) {
		segments.pop();
		const parentPath = "/" + segments.join("/");
		if (!parentPath || parentPath === "/") break;

		item = pathService.getItemByPath(parentPath);
		if (item?.visibleOnMenu) return item;
	}
	return null;
};

const findActiveMenuItems = (activeItem: ICreateBaseItemPathManager) => {
	for (const group of pathItems) {
		for (const item of group.items) {
			if (item.id === activeItem.id) {
				return {
					activeItemId: item.id,
					activeSubItemId: null,
					parentOfActiveSubItemId: null,
				};
			}

			if (item.subItems?.some(sub => sub.id === activeItem.id)) {
				return {
					activeItemId: item.id,
					activeSubItemId: activeItem.id,
					parentOfActiveSubItemId: item.id,
				};
			}
		}
	}

	return {
		activeItemId: activeItem.id,
		activeSubItemId: null,
		parentOfActiveSubItemId: null,
	};
};

export const useActiveMenuItem = (): IUseActiveMenuItemReturn => {
	const { pathname } = useNavigatePaths();
	const [activeMenu, setActiveMenu] = useState<IActiveMenuState>(createInitialState);

	const updateActiveItems = useCallback(() => {
		const activeItem = findClosestVisibleMenuItem(pathname);

		if (!activeItem) {
			setActiveMenu(createInitialState());
			return;
		}

		const newState = findActiveMenuItems(activeItem);
		setActiveMenu(newState);
	}, [pathname]);

	useEffect(() => {
		updateActiveItems();
	}, [updateActiveItems]);

	const isMenuItemActive = useCallback((id: string) => activeMenu.activeItemId === id, [activeMenu.activeItemId]);

	const isSubMenuItemActive = useCallback((id: string) => activeMenu.activeSubItemId === id, [activeMenu.activeSubItemId]);

	return useMemo(
		() => ({
			activeItemId: activeMenu.activeItemId,
			activeSubItemId: activeMenu.activeSubItemId,
			parentOfActiveSubItemId: activeMenu.parentOfActiveSubItemId,
			isMenuItemActive,
			isSubMenuItemActive,
			updateActiveItems,
		}),
		[activeMenu, isMenuItemActive, isSubMenuItemActive, updateActiveItems]
	);
};
