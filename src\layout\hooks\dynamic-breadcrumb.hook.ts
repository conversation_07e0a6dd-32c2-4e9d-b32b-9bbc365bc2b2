"use client";

import { useNavigatePaths } from "@/shared/hooks/utils";
import { useActiveMenuItem } from "./active-menu-item.hook";
import { pathService } from "@/config/path-manager/service";

type BreadcrumbPathItem = Readonly<{
	label: string;
	href?: string;
	description?: string;
	isActive?: boolean;
}>;

const getDynamicLabel = (baseHref: string, pathname: string, params: Readonly<Record<string, string | string[] | undefined>>): string | undefined => {
	const paramMatch = /\[(?:[.]{3})?(.*?)\]/.exec(baseHref);
	if (paramMatch?.[1]) {
		const paramKey = paramMatch[1];
		const paramValue = params[paramKey];
		if (typeof paramValue === "string" && paramValue) return paramValue;
		if (Array.isArray(paramValue) && paramValue.length > 0) return paramValue.join("/");
	}
	if (pathname.startsWith(`${baseHref}/`) && pathname.length > baseHref.length + 1) {
		return pathname.substring(baseHref.length + 1).split("/")[0];
	}
	return undefined;
};

const addUnique = (list: ReadonlyArray<BreadcrumbPathItem>, item: BreadcrumbPathItem): BreadcrumbPathItem[] => {
	return list.some(i => i.label === item.label && i.href === item.href) ? [...list] : [...list, item];
};

export const useDynamicBreadcrumb = (): { uniqueBreadcrumbPath: Readonly<BreadcrumbPathItem[]> } => {
	const { activeItemId, activeSubItemId, parentOfActiveSubItemId } = useActiveMenuItem();
	const { pathname, params } = useNavigatePaths();

	let breadcrumb: BreadcrumbPathItem[] = [{ label: "Tela Inicial", href: "/", isActive: true }];

	if (pathname === "/") return { uniqueBreadcrumbPath: breadcrumb };

	if (parentOfActiveSubItemId) {
		const parent = pathService.getItemById(parentOfActiveSubItemId);
		if (parent && parent.route?.path !== "/") {
			breadcrumb = addUnique(breadcrumb, {
				label: parent.name,
				href: parent.route.path,
				isActive: parent.route.active,
			});
		}
	}

	const currentMenuId = activeSubItemId ?? activeItemId;
	let baseHref: string | undefined;

	if (currentMenuId) {
		const menu = pathService.getItemById(currentMenuId);
		if (menu) {
			baseHref = menu.route?.path;
			if (menu.route?.path !== "/") {
				breadcrumb = addUnique(breadcrumb, {
					label: menu.name,
					href: menu.route.path,
					description: menu.description,
					isActive: menu.route?.active,
				});
			}
		}
	}

	if (baseHref && baseHref !== pathname) {
		const dynamicLabel = getDynamicLabel(baseHref, pathname, params);
		if (dynamicLabel) {
			breadcrumb = addUnique(breadcrumb, { label: dynamicLabel, href: pathname });
		}
	}

	return { uniqueBreadcrumbPath: breadcrumb };
};
