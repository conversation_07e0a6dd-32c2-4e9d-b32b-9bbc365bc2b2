/* Animações para páginas de erro e not-found */
@keyframes fade-in {
	from {
		opacity: 0;
		transform: translateY(12px);
	}
	to {
		opacity: 1;
		transform: none;
	}
}

.animate-fade-in {
	animation: fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-in.delay-100 {
	animation-delay: 0.1s;
}

.animate-fade-in.delay-200 {
	animation-delay: 0.2s;
}

.animate-fade-in.delay-300 {
	animation-delay: 0.3s;
}

.animate-fade-in.delay-400 {
	animation-delay: 0.4s;
}

/* Responsividade para páginas de erro */
@media (max-width: 640px) {
	.max-w-xl {
		max-width: 100%;
	}

	.error-page h1 {
		font-size: 1.75rem;
	}

	.error-page p {
		font-size: 0.875rem;
	}
}

/* Animação específica para página forbidden */
@keyframes fade-in-forbidden {
	from {
		opacity: 0;
		transform: translateY(16px);
	}
	to {
		opacity: 1;
		transform: none;
	}
}

.animate-fade-in-forbidden {
	animation: fade-in-forbidden 0.7s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-in-forbidden.delay-100 {
	animation-delay: 0.1s;
}

.animate-fade-in-forbidden.delay-200 {
	animation-delay: 0.2s;
}

.animate-fade-in-forbidden.delay-300 {
	animation-delay: 0.3s;
}
