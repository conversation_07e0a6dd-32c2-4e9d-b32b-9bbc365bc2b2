import { NextRequest, NextResponse } from "next/server";
import { authMiddleware } from "./config/middleware/auth/auth.middleware";
import { permissionsMiddleware } from "./config/middleware/auth/route.middleware";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

export async function middleware(request: NextRequest): Promise<Response> {
	const { pathname } = request.nextUrl;
	if (pathname === "/login") return SecurityHeadersMiddleware.addToResponse(NextResponse.redirect(new URL("/", request.url)));
	const authResponse = await authMiddleware(request);
	if (authResponse.headers.get("x-middleware-rewrite") || authResponse.headers.get("x-middleware-redirect"))
		return SecurityHeadersMiddleware.addToResponse(authResponse);
	const permissionsResponse = permissionsMiddleware(request);
	if (permissionsResponse.headers.get("x-middleware-rewrite") || permissionsResponse.headers.get("x-middleware-redirect"))
		return SecurityHeadersMiddleware.addToResponse(permissionsResponse);
	return SecurityHeadersMiddleware.addToResponse(NextResponse.next());
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
