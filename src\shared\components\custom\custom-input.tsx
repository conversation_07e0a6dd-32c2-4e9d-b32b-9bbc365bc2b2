import React from "react";
import { Label } from "../shadcn/label";
import { Input } from "../shadcn/input";

interface CustomInputType<T> {
    label: string;
    value: T;
    type?: "text" | "number";
    placeholder?: string;
    name: string;
    onChange: (value: T) => void;
    onBlur?: (value: T) => void;
    className?: string;
}

function CustomInput<T>({
    label,
    value,
    type = "text",
    placeholder,
    name,
    onChange,
    onBlur,
    className = "",
}: CustomInputType<T>) {
    return (
        <div className="flex flex-col items-start w-full gap-1.5 max-w-sm">
            <Label htmlFor={name} className="text-sm text-text-primary">
                {label}
            </Label>
            <Input
                type={type}
                placeholder={placeholder}
                value={
                    value === undefined || value === null
                        ? ""
                        : type === "number"
                          ? String(value)
                          : String(value)
                }
                name={name}
                onChange={(e) => {
                    const inputValue =
                        type === "number"
                            ? e.target.value === ""
                                ? ""
                                : Number(e.target.value)
                            : e.target.value;
                    onChange(inputValue as T);
                }}
                onBlur={
                    onBlur
                        ? (e) => {
                              const inputValue =
                                  type === "number"
                                      ? e.target.value === ""
                                          ? ""
                                          : Number(e.target.value)
                                      : e.target.value;
                              onBlur(inputValue as T);
                          }
                        : undefined
                }
                className={className}
            ></Input>
        </div>
    );
}

export default CustomInput;
