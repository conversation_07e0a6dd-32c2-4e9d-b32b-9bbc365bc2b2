import { useCallback, useEffect, useRef } from "react";

interface IUseClickOutsideOptions {
	enabled?: boolean;
	eventType?: "mousedown" | "mouseup" | "click";
}

export const useClickOutside = <T extends HTMLElement = HTMLElement>(callback: () => void, options: IUseClickOutsideOptions = {}) => {
	const { enabled = true, eventType = "mousedown" } = options;
	const ref = useRef<T>(null);

	const handleClickOutside = useCallback(
		(event: Event) => {
			if (!enabled) return;

			const target = event.target as Node;
			if (ref.current && !ref.current.contains(target)) {
				callback();
			}
		},
		[callback, enabled]
	);

	useEffect(() => {
		if (!enabled) return;

		document.addEventListener(eventType, handleClickOutside);
		return () => {
			document.removeEventListener(eventType, handleClickOutside);
		};
	}, [handleClickOutside, eventType, enabled]);

	return ref;
};
