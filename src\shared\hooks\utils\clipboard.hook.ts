import { useCallback, useState } from "react";

type UseClipboardOptions = {
	timeout?: number;
	onSuccess?: () => void;
	onError?: (error: Error) => void;
};

export const useClipboard = ({ timeout = 2000, onSuccess, onError }: UseClipboardOptions = {}) => {
	const [isCopied, setIsCopied] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const copy = useCallback(
		async (text: string) => {
			try {
				await navigator.clipboard.writeText(text);
				setIsCopied(true);
				setError(null);
				onSuccess?.();
				setTimeout(() => setIsCopied(false), timeout);
			} catch (e) {
				const err = e instanceof Error ? e : new Error("Falha ao copiar para a área de transferência");
				setError(err);
				onError?.(err);
			}
		},
		[timeout, onSuccess, onError]
	);

	return { copy, isCopied, error };
};
