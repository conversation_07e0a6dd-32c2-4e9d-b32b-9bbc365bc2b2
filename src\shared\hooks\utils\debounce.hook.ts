import { useCallback, useEffect, useRef } from "react";

interface IUseDebounceOptions {
	delay: number;
	immediate?: boolean;
}

export const useDebounce = <T extends (...args: unknown[]) => unknown>(callback: T, { delay, immediate = false }: IUseDebounceOptions) => {
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);
	const isFirstCallRef = useRef(true);

	const debouncedCallback = useCallback(
		(...args: Parameters<T>) => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			if (immediate && isFirstCallRef.current) {
				isFirstCallRef.current = false;
				return callback(...args);
			}

			timeoutRef.current = setTimeout(() => {
				callback(...args);
			}, delay);
		},
		[callback, delay, immediate]
	);

	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	return debouncedCallback;
};
