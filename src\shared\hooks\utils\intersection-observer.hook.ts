import { useCallback, useEffect, useRef, useState } from "react";

interface IUseIntersectionObserverOptions {
	root?: Element | null;
	rootMargin?: string;
	threshold?: number | number[];
	once?: boolean;
}

interface IIntersectionObserverEntry {
	isIntersecting: boolean;
	intersectionRatio: number;
	boundingClientRect: DOMRectReadOnly;
	rootBounds: DOMRectReadOnly | null;
	target: Element;
}

export const useIntersectionObserver = (options: IUseIntersectionObserverOptions = {}) => {
	const { root = null, rootMargin = "0px", threshold = 0, once = false } = options;
	const [entry, setEntry] = useState<IIntersectionObserverEntry | null>(null);
	const [isIntersecting, setIsIntersecting] = useState(false);
	const elementRef = useRef<Element | null>(null);
	const observerRef = useRef<IntersectionObserver | null>(null);

	const setElement = useCallback(
		(element: Element | null) => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}

			elementRef.current = element;

			if (element && typeof IntersectionObserver !== "undefined") {
				observerRef.current = new IntersectionObserver(
					([intersectionEntry]) => {
						setEntry(intersectionEntry);
						setIsIntersecting(intersectionEntry.isIntersecting);

						if (once && intersectionEntry.isIntersecting) {
							observerRef.current?.disconnect();
						}
					},
					{ root, rootMargin, threshold }
				);

				observerRef.current.observe(element);
			}
		},
		[root, rootMargin, threshold, once]
	);

	useEffect(() => {
		return () => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}
		};
	}, []);

	return {
		ref: setElement,
		entry,
		isIntersecting,
	};
};
