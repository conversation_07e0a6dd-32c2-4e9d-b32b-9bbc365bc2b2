import { useCallback, useEffect, useState } from "react";

interface IUseLocalStorageOptions<T> {
	serializer?: (value: T) => string;
	deserializer?: (value: string) => T;
}

export const useLocalStorage = <T>(key: string, initialValue: T, options: IUseLocalStorageOptions<T> = {}) => {
	const { serializer = JSON.stringify, deserializer = JSON.parse } = options;

	const [storedValue, setStoredValue] = useState<T>(() => {
		if (typeof window === "undefined") {
			return initialValue;
		}

		try {
			const item = window.localStorage.getItem(key);
			return item ? deserializer(item) : initialValue;
		} catch (error) {
			console.warn(`Erro ao ler localStorage key "${key}":`, error);
			return initialValue;
		}
	});

	const setValue = useCallback(
		(value: T | ((val: T) => T)) => {
			try {
				const valueToStore = value instanceof Function ? value(storedValue) : value;
				setStoredValue(valueToStore);

				if (typeof window !== "undefined") {
					window.localStorage.setItem(key, serializer(valueToStore));
				}
			} catch (error) {
				console.warn(`Erro ao definir localStorage key "${key}":`, error);
			}
		},
		[key, serializer, storedValue]
	);

	const removeValue = useCallback(() => {
		try {
			setStoredValue(initialValue);
			if (typeof window !== "undefined") {
				window.localStorage.removeItem(key);
			}
		} catch (error) {
			console.warn(`Erro ao remover localStorage key "${key}":`, error);
		}
	}, [key, initialValue]);

	useEffect(() => {
		const handleStorageChange = (e: StorageEvent) => {
			if (e.key === key && e.newValue !== null) {
				try {
					setStoredValue(deserializer(e.newValue));
				} catch (error) {
					console.warn(`Erro ao sincronizar localStorage key "${key}":`, error);
				}
			}
		};

		if (typeof window !== "undefined") {
			window.addEventListener("storage", handleStorageChange);
			return () => window.removeEventListener("storage", handleStorageChange);
		}
	}, [key, deserializer]);

	return [storedValue, setValue, removeValue] as const;
};
