import { useEffect, useRef, useState } from "react";

type MeasureRect = {
	x: number;
	y: number;
	width: number;
	height: number;
	top: number;
	right: number;
	bottom: number;
	left: number;
};

const defaultRect: MeasureRect = {
	x: 0,
	y: 0,
	width: 0,
	height: 0,
	top: 0,
	right: 0,
	bottom: 0,
	left: 0,
};

export const useMeasure = <T extends HTMLElement>() => {
	const ref = useRef<T>(null);
	const [rect, setRect] = useState<MeasureRect>(defaultRect);

	useEffect(() => {
		const element = ref.current;
		if (!element) return;

		const resizeObserver = new ResizeObserver(([entry]) => {
			if (entry) {
				setRect(entry.contentRect);
			}
		});

		resizeObserver.observe(element);

		return () => {
			resizeObserver.disconnect();
		};
	}, []);

	return [ref, rect] as const;
};
