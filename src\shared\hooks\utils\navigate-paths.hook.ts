"use client";

import { INavigateOptions, INavigation, TNavigateReplaceOptions, TNavigateToOptions, TPathAppRoutes } from "@/shared/types/navigation/types";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";

export const useNavigatePaths = (): INavigation => {
	const router = useRouter();
	const pathname = usePathname();
	const params = useParams();
	const searchParams = useSearchParams();

	const createUrl = ({ path, query = {}, params }: INavigateOptions): string => {
		let finalPath = path;
		if (params) Object.entries(params).forEach(([k, v]) => (finalPath = finalPath.replace(`:${k}`, v)));
		const url = new URL(finalPath, window.location.origin);
		const currentParams = new URLSearchParams(searchParams);
		Object.entries(query).forEach(([k, v]) => currentParams.set(k, v));
		url.search = currentParams.toString();
		return url.toString().replace(window.location.origin, "");
	};

	const navigateTo = <T extends TPathAppRoutes>(options: TNavigateToOptions<T>) => {
		const url = createUrl(options as INavigateOptions);
		if (options.replace) {
			router.replace(url);
		} else {
			router.push(url);
		}
	};

	const replace = <T extends TPathAppRoutes>(options: TNavigateReplaceOptions<T>) => {
		router.replace(createUrl(options as INavigateOptions));
	};

	const replaceToCurrent = (options: { query?: Record<string, string>; params?: Record<string, string> } = {}) => {
		router.replace(createUrl({ path: pathname, ...options }));
	};

	const updateSearchParams = (params: Record<string, string>) => {
		const current = new URLSearchParams(searchParams);
		Object.entries(params).forEach(([k, v]) => current.set(k, v));
		router.push(createUrl({ path: pathname, query: Object.fromEntries(current.entries()) }));
	};

	const removeSearchParam = (key: string) => {
		const current = new URLSearchParams(searchParams);
		current.delete(key);
		router.push(createUrl({ path: pathname, query: Object.fromEntries(current.entries()) }));
	};

	return {
		navigateTo,
		replace,
		replaceToCurrent,
		navigateBack: router.back,
		navigateForward: router.forward,
		refresh: router.refresh,
		pathname,
		searchParams,
		updateSearchParams,
		removeSearchParam,
		createUrl,
		params,
	};
};
