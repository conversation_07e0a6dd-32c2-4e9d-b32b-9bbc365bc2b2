import { useCallback, useState } from "react";

interface PaginationProps {
	initialPage?: number;
	initialLimit?: number;
	total?: number;
}

export const usePagination = ({ initialPage = 1, initialLimit = 10, total = 0 }: PaginationProps = {}) => {
	const [currentPage, setCurrentPage] = useState(initialPage);
	const [limit, setLimit] = useState(initialLimit);

	const totalPages = Math.max(1, Math.ceil(total / limit));
	const offset = (currentPage - 1) * limit;

	const isFirstPage = currentPage === 1;
	const isLastPage = currentPage === totalPages;

	const nextPage = useCallback(() => {
		setCurrentPage(p => (p < totalPages ? p + 1 : p));
	}, [totalPages]);

	const previousPage = useCallback(() => {
		setCurrentPage(p => (p > 1 ? p - 1 : p));
	}, []);

	const goToPage = useCallback((page: number) => setCurrentPage(Math.max(1, Math.min(page, totalPages))), [totalPages]);

	const changeLimit = useCallback(
		(newLimit: number) => {
			setLimit(newLimit);
			const newTotalPages = Math.max(1, Math.ceil(total / newLimit));
			setCurrentPage(p => (p > newTotalPages ? newTotalPages : p));
		},
		[total]
	);

	return {
		currentPage,
		setCurrentPage,
		limit,
		offset,
		totalPages,
		isFirstPage,
		isLastPage,
		nextPage,
		previousPage,
		goToPage,
		changeLimit,
	};
};
