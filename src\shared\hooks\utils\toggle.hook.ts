import { useCallback, useState } from "react";

export const useToggle = (initialValue: boolean = false) => {
	const [value, setValue] = useState(initialValue);

	const toggle = useCallback(() => {
		setValue(prev => !prev);
	}, []);

	const setTrue = useCallback(() => {
		setValue(true);
	}, []);

	const setFalse = useCallback(() => {
		setValue(false);
	}, []);

	const setValueCallback = useCallback((newValue: boolean) => {
		setValue(newValue);
	}, []);

	return {
		value,
		toggle,
		setTrue,
		setFalse,
		setValue: setValueCallback,
	};
};
