import { useEffect, useState } from "react";

interface IWindowSize {
	width: number;
	height: number;
}

const getWindowSize = (): IWindowSize => {
	if (typeof window === "undefined") {
		return { width: 0, height: 0 };
	}

	return {
		width: window.innerWidth,
		height: window.innerHeight,
	};
};

export const useWindowSize = () => {
	const [windowSize, setWindowSize] = useState<IWindowSize>(getWindowSize);

	useEffect(() => {
		if (typeof window === "undefined") return;

		const handleResize = () => {
			setWindowSize(getWindowSize());
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	return windowSize;
};
