export const COOKIE_ERROR_MESSAGES = {
	INVALID_COOKIE: "O cookie não é válido no sistema",
	MISSING_NAME: "Nome do cookie é obrigatório",
	MISSING_VALUE: "Valor do cookie é obrigatório",
	NOT_FOUND: (name: string) => `O cookie ${name} não foi encontrado`,
	GENERIC_ERROR: (error: string) => `Erro ao processar cookie: ${error}`,
} as const;

export const COOKIE_SUCCESS_MESSAGES = {
	CREATED: (name: string) => `Cookie ${name} criado com sucesso`,
	REMOVED: (name: string) => `Cookie ${name} removido com sucesso`,
	FOUND: (name: string) => `<PERSON>ie ${name} encontrado`,
} as const;

export const COOKIE_SECRET = process.env.COOKIE_SECRET_SIGNATURE as string;

export const HTTP_STATUS = {
	OK: 200,
	CREATED: 201,
	BAD_REQUEST: 400,
	NOT_FOUND: 404,
	INTERNAL_ERROR: 500,
} as const;

export const SIGNATURE_CONFIG = {
	VERSION: "v1",
	PREFIX: process.env.COOKIE_PREFIX,
	SEPARATOR: "@$",
	HMAC_ALGORITHM: "SHA-256",
	MIN_VALUE_LENGTH: 1,
	MAX_VALUE_LENGTH: 4096,
	TIMESTAMP_MAX_AGE: 1000 * 60 * 60 * 24 * 7,
} as const;
