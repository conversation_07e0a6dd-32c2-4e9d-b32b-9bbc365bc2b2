"use server";

import { ApiResponse } from "@/shared/types/requests/request.type";
import { ICreateCookieProps } from "../types";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { DEFAULT_COOKIE_OPTIONS } from "@/config/cookies/options";
import { signCookie } from "../signature/sign";
import { cookies } from "next/headers";
import { getCookieName } from "../utils/get-cookie-name";

export const createCookie = async (data: ICreateCookieProps): Promise<ApiResponse<IMessageGlobalReturn>> => {
	try {
		const cookiesOptions = { ...DEFAULT_COOKIE_OPTIONS, ...data.options };
		const cookieEncoded = await signCookie({ value: data.value });
		const cookieStore = await cookies();
		cookieStore.set(getCookieName(data.name), cookieEncoded, cookiesOptions);
		return {
			success: true,
			data: {
				message: `<PERSON><PERSON> ${data.name} criado com sucesso`,
			},
			status: 201,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro ao criar cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
