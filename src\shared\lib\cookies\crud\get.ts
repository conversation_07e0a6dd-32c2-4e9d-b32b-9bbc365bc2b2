"use server";
import { cookies } from "next/headers";
import { ICookieGetResult } from "../types";
import { validateSignature } from "../signature/config";
import { getCookieName } from "../utils/get-cookie-name";
import { unSignCookie } from "../signature/unsign";

interface IGetCookieProps {
	name: string;
	returnSigned?: boolean;
}

export const getCookie = async ({ name, returnSigned = false }: IGetCookieProps): Promise<ICookieGetResult> => {
	try {
		const cookieStore = await cookies();
		const cookieName = getCookieName(name);
		const cookie = cookieStore.get(cookieName);
		if (!cookie) return { success: false, message: `O cookie ${name} não foi encontrado`, value: null };
		if (!(await validateSignature(cookie.value)).isValid)
			return { success: false, message: `O cookie ${name} não é válido no sistema`, value: null };
		const cookieValue = returnSigned ? cookie.value : unSignCookie(cookie.value) ?? cookie.value;
		return { success: true, message: `O cookie ${name} foi encontrado`, value: cookieValue };
	} catch (error: unknown) {
		const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
		return { success: false, message: `Erro ao buscar cookie: ${errorMessage}`, value: null };
	}
};
