"use server";

import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { cookies } from "next/headers";
import { getCookieName } from "../utils/get-cookie-name";

interface IRemoveCookieProps {
	name: string;
}

export const removeCookie = async ({ name }: IRemoveCookieProps): Promise<ApiResponse<IMessageGlobalReturn>> => {
	try {
		const cookieStore = await cookies();
		const cookieName = getCookieName(name);
		if (!cookieStore.has(cookieName)) {
			return {
				success: false,
				data: {
					message: `O cookie ${name} não foi encontrado`,
				},
				status: 404,
			};
		}
		cookieStore.delete(cookieName);
		return {
			success: true,
			data: {
				message: `O cookie ${name} foi removido com sucesso`,
			},
			status: 200,
		};
	} catch (error: unknown) {
		return {
			success: false,
			data: {
				message: `Erro ao remover cookie: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};
