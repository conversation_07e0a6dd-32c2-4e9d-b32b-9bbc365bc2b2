import { COOKIE_SECRET, SIGNATURE_CONFIG } from "../constants";

interface IValidationResult {
	isValid: boolean;
	value?: string;
	error?: string;
}

export class SignatureError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "SignatureError";
	}
}

export class InvalidValueError extends SignatureError {
	constructor(message: string) {
		super(message);
		this.name = "InvalidValueError";
	}
}

let cachedKey: CryptoKey | null = null;

export function bufferToHex(buffer: ArrayBuffer): string {
	return Array.from(new Uint8Array(buffer))
		.map(b => b.toString(16).padStart(2, "0"))
		.join("");
}

export async function getHMACKey(): Promise<CryptoKey> {
	if (cachedKey) return cachedKey;
	const encoder = new TextEncoder();
	cachedKey = await crypto.subtle.importKey("raw", encoder.encode(COOKIE_SECRET), { name: "HM<PERSON>", hash: "SHA-256" }, false, ["sign"]);
	return cachedKey;
}

function validateTimestamp(timestamp: number): boolean {
	const now = Date.now();
	const age = now - timestamp;
	return age >= 0 && age <= SIGNATURE_CONFIG.TIMESTAMP_MAX_AGE;
}

async function verifySignature(value: string, timestamp: number, providedDigest: string): Promise<boolean> {
	const key = await getHMACKey();
	const dataToSign = [value, timestamp, SIGNATURE_CONFIG.VERSION].join(SIGNATURE_CONFIG.SEPARATOR);
	try {
		const signature = await crypto.subtle.sign(
			{ name: "HMAC", hash: SIGNATURE_CONFIG.HMAC_ALGORITHM },
			key,
			new TextEncoder().encode(dataToSign)
		);
		const calculatedDigest = bufferToHex(signature);
		return calculatedDigest === providedDigest;
	} catch (error: unknown) {
		const message = error instanceof Error ? error.message : String(error);
		throw new SignatureError(`Erro ao verificar assinatura: ${message}`);
	}
}

export async function validateSignature(signedValue: string): Promise<IValidationResult> {
	try {
		const [prefix, version, value, timestamp, digest] = signedValue.split(SIGNATURE_CONFIG.SEPARATOR);
		if (!process.env.COOKIE_PREFIX) throw new Error("Prefixo do cookie não definido nas variáveis de ambiente");
		if (!prefix || prefix !== process.env.COOKIE_PREFIX) return { isValid: false, error: "Prefixo do cookie inválido" };
		if (version !== SIGNATURE_CONFIG.VERSION) return { isValid: false, error: "Versão da assinatura inválida" };
		if (!value || !timestamp || !digest) return { isValid: false, error: "Formato da assinatura inválido" };
		const timestampNum = parseInt(timestamp, 10);
		if (isNaN(timestampNum) || !validateTimestamp(timestampNum)) return { isValid: false, error: "Timestamp inválido ou expirado" };
		const isSignatureValid = await verifySignature(value, timestampNum, digest);
		if (!isSignatureValid) return { isValid: false, error: "Assinatura inválida" };
		return { isValid: true, value };
	} catch (error) {
		return {
			isValid: false,
			error: error instanceof SignatureError ? error.message : "Erro ao validar assinatura",
		};
	}
}

export function validateValue(value: string): void {
	if (!value || typeof value !== "string") throw new InvalidValueError("O valor do cookie não pode estar vazio");
	if (value.length < SIGNATURE_CONFIG.MIN_VALUE_LENGTH || value.length > SIGNATURE_CONFIG.MAX_VALUE_LENGTH) {
		throw new InvalidValueError(
			`O valor do cookie deve ter entre ${SIGNATURE_CONFIG.MIN_VALUE_LENGTH} e ${SIGNATURE_CONFIG.MAX_VALUE_LENGTH} caracteres`
		);
	}
}
