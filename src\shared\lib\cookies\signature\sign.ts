import { SIGNATURE_CONFIG } from "../constants";
import { bufferToHex, getH<PERSON><PERSON><PERSON><PERSON>, SignatureError, validateValue } from "./config";

async function createSignature(value: string, timestamp: number): Promise<string> {
	const dataToSign = [value, timestamp, SIGNATURE_CONFIG.VERSION].join(SIGNATURE_CONFIG.SEPARATOR);
	const key = await getHMACKey();
	try {
		const signature = await crypto.subtle.sign(
			{ name: "HMAC", hash: SIGNATURE_CONFIG.HMAC_ALGORITHM },
			key,
			new TextEncoder().encode(dataToSign)
		);
		return bufferToHex(signature);
	} catch (error: unknown) {
		const message = error instanceof Error ? error.message : String(error);
		throw new SignatureError(`Erro ao criar assinatura: ${message}`);
	}
}

export async function signCookie({ value }: { value: string }): Promise<string> {
	validateValue(value);
	const timestamp = Date.now();
	const digest = await createSignature(value, timestamp);
	return [process.env.COOKIE_PREFIX, SIGNATURE_CONFIG.VERSION, value, timestamp, digest].join(SIGNATURE_CONFIG.SEPARATOR);
}
