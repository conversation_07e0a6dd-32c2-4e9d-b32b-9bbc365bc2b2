import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";

export interface ICreateCookieProps {
	name: string;
	value: string;
	options?: ICookieOptions;
}

export interface ICookieOptions {
	maxAge: number;
	expires?: Date;
	path?: string;
	secure?: boolean;
	sameSite?: "strict" | "lax" | "none";
	httpOnly?: boolean;
}

export type TCreateCookie = (props: ICreateCookieProps) => void;

export interface ICookieOptions {
	signed?: boolean;
}

export interface ICookieValidationResult {
	isValid: boolean;
	error?: string;
}

export interface ICookieValue {
	value: string;
	options?: ICookieOptions;
}

export interface ICookieManager {
	create(data: ICreateCookieProps): Promise<ApiResponse<IMessageGlobalReturn>>;
	get(name: string, returnSigned?: boolean): Promise<ICookieGetResult>;
	remove(name: string): Promise<ApiResponse<IMessageGlobalReturn>>;
}

export interface ICookieGetResult {
	success: boolean;
	message: string;
	value: string | null;
}
export interface ICookieGetResult {
	success: boolean;
	message: string;
	value: string | null;
}
