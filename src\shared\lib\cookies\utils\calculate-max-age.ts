import { getJWTExpiration } from "../../jwt/expiration-time";

export const calculateMaxAge = (token: string): number | null => {
	const expirationDate = getJWTExpiration(token);
	if (!expirationDate) return null;
	const expTime = expirationDate.getTime();
	const now = Date.now();
	if (expTime - now < 0) return null;
	const diff = expTime - now;
	const maxAge = Math.floor(diff / 1000);
	return maxAge;
};
