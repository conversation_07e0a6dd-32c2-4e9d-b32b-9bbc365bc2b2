import { AxiosError } from "axios";
import { DEFAULT_ERROR_MESSAGES } from "./constants";
import { IHandleResponseError } from "@/shared/types/requests/request.type";

function extractErrorMessage(data: unknown): string | null {
	if (!data || typeof data !== "object") return null;
	const errorData = data as Record<string, unknown>;
	if (typeof errorData.message === "string") return errorData.message;
	if (Array.isArray(errorData.message)) return errorData.message.filter(Boolean).join(". ");
	if (typeof errorData.error === "string") return errorData.error;
	if (typeof errorData.detail === "string") return errorData.detail;
	return null;
}

export function handleGlobalError(error: unknown): IHandleResponseError {
	if (error instanceof AxiosError) {
		const status = error.response?.status ?? 0;
		const customMessage = error.response?.data ? extractErrorMessage(error.response.data) : null;
		const message = customMessage || DEFAULT_ERROR_MESSAGES[status] || error.message;
		return {
			success: false,
			data: {
				message,
				method: error.config?.method,
				url: error.config?.url,
				details: error.response?.data,
			},
			status: status,
		};
	}
	if (error instanceof Error) {
		return {
			success: false,
			data: {
				message: error.message,
				method: undefined,
				url: undefined,
				details: undefined,
			},
			status: 500,
		};
	}
	return {
		success: false,
		data: {
			message: DEFAULT_ERROR_MESSAGES[500],
			method: undefined,
			url: undefined,
			details: undefined,
		},
		status: 500,
	};
}
