import { decodeJWT } from "./decode";

export const getJWTMaxAge = (token: string): number | null => {
	const decodedPayload = decodeJWT(token);
	if (!decodedPayload?.exp || typeof decodedPayload.exp !== "number") return null;
	const currentTimeInSeconds = Math.floor(Date.now() / 1000);
	const expirationTimeInSeconds = decodedPayload.exp;
	const maxAge = expirationTimeInSeconds - currentTimeInSeconds;
	return maxAge > 0 ? maxAge : null;
};
