import { IRole, ROLES } from "@/config/enums/role.enum";

export class RoleNotFoundError extends Error {
	constructor(roleId: number) {
		super(`Permissão com ID ${roleId} não encontrada`);
		this.name = "RoleNotFoundError";
	}
}

export const validateRole = (roleId: number): roleId is IRole => {
	return Object.values(ROLES).includes(roleId as IRole);
};

export const getRoleName = (roleId: number): string => {
	if (!validateRole(roleId)) throw new RoleNotFoundError(roleId);
	const roleName = Object.entries(ROLES).find(([, value]) => value === roleId)?.[0];
	if (!roleName) throw new RoleNotFoundError(roleId);
	return roleName
		.split("_")
		.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join(" ");
};

export const fetchRoleName = (roleId: number): string | undefined => {
	try {
		return getRoleName(roleId);
	} catch (error) {
		if (error instanceof RoleNotFoundError) return undefined;
		throw error;
	}
};

export const getHighestRoleName = (roles: number[] = []): string => {
	if (!roles.length) return "Usuário";
	const validRoles = roles.filter(validateRole);
	if (!validRoles.length) return "Usuário";
	const highest = Math.max(...validRoles);
	return getRoleName(highest);
};
