export const DEFAULT_TIMEOUT = 30000;

export const CACHE_TTL = {
	SHORT: 5 * 60 * 1000,
	MEDIUM: 15 * 60 * 1000,
	LONG: 60 * 60 * 1000,
} as const;

export const MAX_CACHE_SIZE = 200;
export const PREFETCH_THRESHOLD = 0.8;

export const MAX_BATCH_REQUESTS = 10;
export const REQUEST_IDLE_TIMEOUT = 100;

export const MAX_CONCURRENT_REQUESTS = 6;
export const MAX_PARALLEL_GROUPS = 3;

export const DEFAULT_RETRY_ATTEMPTS = 3;
export const DEFAULT_RETRY_DELAY = 1000;
export const RETRY_BACKOFF_FACTOR = 1.5;
export const RETRY_JITTER = 0.2;
