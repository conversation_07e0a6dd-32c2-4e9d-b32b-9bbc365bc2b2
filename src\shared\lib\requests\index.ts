import { axiosInstance } from "@/config/api/instance";
import { ApiResponse, IHandleMetadataAxiosRequestConfig } from "@/shared/types/requests/request.type";
import { ICreateRequest } from "@/shared/types/requests/create-request.type";
import { URLValidator } from "./security/url-validator";
import { RequestManager } from "./client/request";
import { RequestCache } from "./cache/request-cache";
import { DEFAULT_TIMEOUT, DEFAULT_RETRY_ATTEMPTS, DEFAULT_RETRY_DELAY, RETRY_BACKOFF_FACTOR, RETRY_JITTER } from "./constants";
import { handleGlobalError } from "../errors";
import { AxiosError } from "axios";

const urlValidator = new URLValidator();
const requestManager = new RequestManager();
const requestCache = new RequestCache();

export async function createRequest<TSuccess, TRequest = unknown>(params: ICreateRequest<TRequest>): Promise<ApiResponse<TSuccess>> {
	try {
		const urlValidation = urlValidator.validateAndSanitizeURL(params.path);
		if (!urlValidation.isValid) {
			return {
				success: false,
				data: {
					message: urlValidation.reason || "URL inválida",
					method: params.method,
					url: params.path,
				},
				status: 400,
			};
		}

		const config: IHandleMetadataAxiosRequestConfig = {
			...params,
			url: urlValidation.sanitizedURL,
			method: params.method,
			data: params.body,
			timeout: params.timeout || DEFAULT_TIMEOUT,
			baseURL: params.baseURL || axiosInstance.defaults.baseURL,
		};

		if (params.method === "GET" && params.useCache === true) {
			const cachedData = requestCache.get<TSuccess>(config.url!);
			if (cachedData) {
				return {
					success: true,
					data: cachedData,
					status: 200,
				};
			}
		}

		const requestKey = `${config.method}:${config.url}`;
		const pendingRequest = requestManager.getPendingRequest(requestKey);
		if (pendingRequest) {
			return (await pendingRequest) as ApiResponse<TSuccess>;
		}
		const abortController = requestManager.createAbortController(requestKey);
		config.signal = abortController.signal;

		const promise = executeRequestWithRetry<TSuccess>(config, params);
		requestManager.registerPendingRequest(requestKey, promise);

		const response = await promise;
		if (response.success && params.method === "GET" && params.useCache === true) requestCache.set(config.url!, response.data);
		return response;
	} catch (error) {
		return handleGlobalError(error as Error);
	}
}

async function executeRequestWithRetry<TSuccess>(config: IHandleMetadataAxiosRequestConfig, params: ICreateRequest): Promise<ApiResponse<TSuccess>> {
	const maxRetries = params.retry !== false ? params.retryAttempts || DEFAULT_RETRY_ATTEMPTS : 0;
	let lastError: unknown;

	for (let attempt = 0; attempt <= maxRetries; attempt++) {
		try {
			const response = await axiosInstance.request<TSuccess>(config);

			return {
				success: true,
				data: response.data,
				status: response.status,
			};
		} catch (error: unknown) {
			lastError = error;

			if (
				error instanceof AxiosError &&
				(error.code === "ECONNABORTED" || (error.response?.status && error.response?.status >= 400 && error.response?.status < 500))
			) {
				break;
			}

			if (attempt < maxRetries) {
				const baseDelay = DEFAULT_RETRY_DELAY * Math.pow(RETRY_BACKOFF_FACTOR, attempt);
				const jitter = baseDelay * RETRY_JITTER * (Math.random() - 0.5);
				const delay = Math.round(baseDelay + jitter);

				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}
	}

	return handleGlobalError(lastError);
}

export function cancelRequest(method: string, url: string): boolean {
	const requestKey = `${method}:${url}`;
	return requestManager.cancelRequest(requestKey);
}

export function clearCache(url?: string): void {
	if (url) {
		requestCache.delete(url);
	} else {
		requestCache.clear();
	}
}

export * from "./helpers/request-helpers";
export * from "./interceptors/request-interceptors";
export { RequestManager } from "./client/request";
export { RequestCache } from "./cache/request-cache";
export { URLValidator } from "./security/url-validator";
