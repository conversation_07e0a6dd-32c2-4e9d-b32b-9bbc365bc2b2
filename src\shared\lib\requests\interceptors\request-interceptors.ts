import { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { axiosInstance } from "@/config/api/instance";

export interface IRequestInterceptor {
	onRequest?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
	onRequestError?: (error: AxiosError) => Promise<never>;
}

export interface IResponseInterceptor {
	onResponse?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
	onResponseError?: (error: AxiosError) => Promise<never>;
}

class InterceptorManager {
	private requestInterceptorIds: number[] = [];
	private responseInterceptorIds: number[] = [];

	addRequestInterceptor(interceptor: IRequestInterceptor): () => void {
		const id = axiosInstance.interceptors.request.use(interceptor.onRequest, interceptor.onRequestError);

		this.requestInterceptorIds.push(id);

		return () => this.removeRequestInterceptor(id);
	}

	addResponseInterceptor(interceptor: IResponseInterceptor): () => void {
		const id = axiosInstance.interceptors.response.use(interceptor.onResponse, interceptor.onResponseError);

		this.responseInterceptorIds.push(id);

		return () => this.removeResponseInterceptor(id);
	}

	private removeRequestInterceptor(id: number): void {
		axiosInstance.interceptors.request.eject(id);
		this.requestInterceptorIds = this.requestInterceptorIds.filter(i => i !== id);
	}

	private removeResponseInterceptor(id: number): void {
		axiosInstance.interceptors.response.eject(id);
		this.responseInterceptorIds = this.responseInterceptorIds.filter(i => i !== id);
	}

	clearAllInterceptors(): void {
		this.requestInterceptorIds.forEach(id => axiosInstance.interceptors.request.eject(id));
		this.responseInterceptorIds.forEach(id => axiosInstance.interceptors.response.eject(id));
		this.requestInterceptorIds = [];
		this.responseInterceptorIds = [];
	}
}

export const interceptorManager = new InterceptorManager();

export function createAuthInterceptor(getToken: () => Promise<string | null>): IRequestInterceptor {
	return {
		onRequest: async config => {
			const token = await getToken();
			if (token && config.headers) config.headers.Authorization = `Bearer ${token}`;
			return config;
		},
	};
}

export function createCaseTransformerInterceptor(): IResponseInterceptor {
	const snakeToCamel = (str: string): string => str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

	const transformKeys = (obj: unknown): unknown => {
		if (Array.isArray(obj)) return obj.map(transformKeys);
		if (obj !== null && typeof obj === "object") {
			return Object.keys(obj).reduce((acc, key) => {
				const camelKey = snakeToCamel(key);
				acc[camelKey] = transformKeys((obj as Record<string, unknown>)[key]);
				return acc;
			}, {} as Record<string, unknown>);
		}
		return obj;
	};

	return {
		onResponse: response => {
			if (response.data) {
				response.data = transformKeys(response.data);
			}
			return response;
		},
	};
}
