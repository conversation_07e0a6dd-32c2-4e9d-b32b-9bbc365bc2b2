interface IURLValidationResult {
	isValid: boolean;
	reason?: string;
}

interface IURLSanitizationResult extends IURLValidationResult {
	sanitizedURL: string;
}

interface IURLValidatorConfig {
	allowedDomains?: string[];
	allowedPatterns?: RegExp[];
}

export class URLValidator {
	private readonly allowedDomains: string[];
	private readonly allowedPatterns: RegExp[];

	constructor(config: IURLValidatorConfig = {}) {
		this.allowedDomains = config.allowedDomains ?? [];
		this.allowedPatterns = config.allowedPatterns ?? [];
	}

	validateURL(url: string): IURLValidationResult {
		try {
			if (this.isRelativePath(url)) return { isValid: true };
			const parsedUrl = this.parseURL(url);
			const protocolValidation = this.validateProtocol(parsedUrl);
			if (!protocolValidation.isValid) return protocolValidation;
			const domainValidation = this.validateDomain(parsedUrl.hostname);
			if (!domainValidation.isValid) return domainValidation;
			const patternValidation = this.validatePattern(url);
			if (!patternValidation.isValid) return patternValidation;
			return { isValid: true };
		} catch {
			return { isValid: false, reason: "URL mal formada" };
		}
	}

	validateAndSanitizeURL(url: string): IURLSanitizationResult {
		const validation = this.validateURL(url);

		if (!validation.isValid) {
			return {
				isValid: false,
				sanitizedURL: "",
				reason: validation.reason,
			};
		}

		return {
			isValid: true,
			sanitizedURL: this.sanitizeURL(url),
		};
	}

	sanitizeURL(url: string): string {
		return url.replace(/[^\w\s:/.?=&%-]/g, "");
	}

	private isRelativePath(url: string): boolean {
		return url.startsWith("/") && !url.startsWith("//");
	}

	private parseURL(url: string): URL {
		const baseURL = typeof window !== "undefined" ? window.location.origin : undefined;
		return new URL(url, baseURL);
	}

	private validateProtocol(parsedUrl: URL): IURLValidationResult {
		const allowedProtocols = ["http:", "https:"];
		if (!allowedProtocols.includes(parsedUrl.protocol)) {
			return {
				isValid: false,
				reason: "Apenas protocolos HTTP e HTTPS são permitidos.",
			};
		}
		return { isValid: true };
	}

	private validateDomain(hostname: string): IURLValidationResult {
		if (this.allowedDomains.length === 0) return { isValid: true };
		const isAllowed = this.allowedDomains.some(domain => hostname === domain || hostname.endsWith(`.${domain}`));
		if (!isAllowed) {
			return {
				isValid: false,
				reason: `O domínio '${hostname}' não está na lista de domínios permitidos.`,
			};
		}
		return { isValid: true };
	}

	private validatePattern(url: string): IURLValidationResult {
		if (this.allowedPatterns.length === 0) return { isValid: true };
		const matchesPattern = this.allowedPatterns.some(pattern => pattern.test(url));
		if (!matchesPattern) {
			return {
				isValid: false,
				reason: "A URL não corresponde a nenhum dos padrões permitidos.",
			};
		}
		return { isValid: true };
	}
}
