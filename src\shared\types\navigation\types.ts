import { PATHS_CONFIG, TPathManagerConfigKey } from "@/config/path-manager/config";
import { Params } from "next/dist/server/request/params";

type TExtractAllSubPaths<T> = T extends { path: string; subPaths: infer S }
	? T extends { active: true }
		? T["path"] | TExtractAllSubPaths<S[keyof S]>
		: TExtractAllSubPaths<S[keyof S]>
	: T extends { path: string; active: true }
	? T["path"]
	: never;

export type TPathAppRoutes = {
	[K in TPathManagerConfigKey]: TExtractAllSubPaths<(typeof PATHS_CONFIG)[K]>;
}[TPathManagerConfigKey];

export type TRouteParamsExtract<T extends string> = T extends `${string}:${infer Param}/${infer Rest}`
	? { [K in Param | keyof TRouteParamsExtract<Rest>]: string }
	: T extends `${string}:${infer Param}`
	? { [K in Param]: string }
	: Record<string, never>;

export type TRouteHasParams<T extends string> = T extends `${string}:${string}` ? true : false;

export type TNavigateToOptions<T extends TPathAppRoutes> = T extends string
	? TRouteHasParams<T> extends true
		? {
				path: T;
				params: TRouteParamsExtract<T>;
				query?: Record<string, string>;
				replace?: boolean;
		  }
		: {
				path: T;
				query?: Record<string, string>;
				replace?: boolean;
				params?: TRouteParamsExtract<T>;
		  }
	: {
			path: T;
			query?: Record<string, string>;
			replace?: boolean;
			params?: Record<string, string>;
	  };

export type TNavigateReplaceOptions<T extends TPathAppRoutes> = Omit<TNavigateToOptions<T>, "replace">;

export interface INavigateOptions {
	path: string;
	query?: Record<string, string>;
	replace?: boolean;
	params?: Record<string, string>;
}

export interface INavigation {
	navigateTo: <T extends TPathAppRoutes>(options: TNavigateToOptions<T>) => void;
	navigateBack: () => void;
	navigateForward: () => void;
	refresh: () => void;
	pathname: string;
	params: Params;
	searchParams: URLSearchParams;
	updateSearchParams: (params: Record<string, string>) => void;
	removeSearchParam: (key: string) => void;
	createUrl: (options: INavigateOptions) => string;
	replace: <T extends TPathAppRoutes>(options: TNavigateReplaceOptions<T>) => void;
	replaceToCurrent: (options?: { query?: Record<string, string>; params?: Record<string, string> }) => void;
}
